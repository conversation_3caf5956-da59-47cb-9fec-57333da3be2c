package com.github.cret.web.oee.domain.analyze;

public class OeeRawResult {

	/**
	 * 线体编码
	 */
	private String lineCode;

	/**
	 * 计划时间
	 */
	private Double planTime;

	/**
	 * 实际计划时间
	 */
	private Double actualPlanTime;

	/**
	 * 运行时间
	 */
	private Double runTime;

	/**
	 * 停止时间
	 */
	private Double stopTime;

	/**
	 * 实际停止时间
	 */
	private Double actualStopTime;

	/**
	 * 换线次数
	 */
	private Integer changeoverNum;

	/**
	 * 换线时间
	 */
	private Long changeoverTime;

	/**
	 * 理论数量
	 */
	private Integer planBoard;

	/**
	 * 生产数量
	 */
	private Integer actualBoard;

	/**
	 * 计划产品点数
	 */
	private Integer planBoardPoints;

	/**
	 * 实际产品点数
	 */
	private Integer actualBoardPoints;

	/**
	 * 不良数量
	 */
	private Integer defectCount;

	/**
	 * 一次通过率下的不良数量
	 */
	private Integer firstPassDefectCount;

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public Double getPlanTime() {
		return planTime;
	}

	public void setPlanTime(Double planTime) {
		this.planTime = planTime;
	}

	public Double getActualPlanTime() {
		return actualPlanTime;
	}

	public void setActualPlanTime(Double actualPlanTime) {
		this.actualPlanTime = actualPlanTime;
	}

	public Double getRunTime() {
		return runTime;
	}

	public void setRunTime(Double runTime) {
		this.runTime = runTime;
	}

	public Double getStopTime() {
		return stopTime;
	}

	public void setStopTime(Double stopTime) {
		this.stopTime = stopTime;
	}

	public Double getActualStopTime() {
		return actualStopTime;
	}

	public void setActualStopTime(Double actualStopTime) {
		this.actualStopTime = actualStopTime;
	}

	public Integer getChangeoverNum() {
		return changeoverNum;
	}

	public void setChangeoverNum(Integer changeoverNum) {
		this.changeoverNum = changeoverNum;
	}

	public Long getChangeoverTime() {
		return changeoverTime;
	}

	public void setChangeoverTime(Long changeoverTime) {
		this.changeoverTime = changeoverTime;
	}

	public Integer getPlanBoard() {
		return planBoard;
	}

	public void setPlanBoard(Integer planBoard) {
		this.planBoard = planBoard;
	}

	public Integer getActualBoard() {
		return actualBoard;
	}

	public void setActualBoard(Integer actualBoard) {
		this.actualBoard = actualBoard;
	}

	public Integer getPlanBoardPoints() {
		return planBoardPoints;
	}

	public void setPlanBoardPoints(Integer planBoardPoints) {
		this.planBoardPoints = planBoardPoints;
	}

	public Integer getActualBoardPoints() {
		return actualBoardPoints;
	}

	public void setActualBoardPoints(Integer actualBoardPoints) {
		this.actualBoardPoints = actualBoardPoints;
	}

	public Integer getDefectCount() {
		return defectCount;
	}

	public void setDefectCount(Integer defectCount) {
		this.defectCount = defectCount;
	}

	public Integer getFirstPassDefectCount() {
		return firstPassDefectCount;
	}

	public void setFirstPassDefectCount(Integer firstPassDefectCount) {
		this.firstPassDefectCount = firstPassDefectCount;
	}

}
