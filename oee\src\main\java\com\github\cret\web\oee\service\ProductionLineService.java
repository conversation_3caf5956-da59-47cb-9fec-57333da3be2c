package com.github.cret.web.oee.service;

import java.util.List;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.ProductionLine;

/**
 * 生产线服务接口，提供与生产线相关的业务逻辑操作。
 */
public interface ProductionLineService {

	/**
	 * 获取所有生产线的列表。
	 * @return 返回所有生产线的列表。
	 */
	List<ProductionLine> getList();

	/**
	 * 根据车间编码查找对应的生产线列表。
	 * @param workShopId 车间ID
	 * @return 返回与指定车间ID相关的生产线列表。
	 */
	List<ProductionLine> findProdutionLineByWorkshopCode(String workShopId);

	/**
	 * 根据生产线代码查找对应的生产线。
	 * @param code 生产线代码
	 * @return 返回与指定代码匹配的生产线对象。
	 */
	ProductionLine findProductionLineBycode(String code);

	/**
	 * 创建新的生产线。
	 * @param productionLine 要创建的生产线对象
	 * @return 返回创建后的生产线对象。
	 */
	ProductionLine createProductionLine(ProductionLine productionLine);

	/**
	 * 更新现有的生产线。
	 * @param productionLine 要更新的生产线对象
	 * @return 返回更新后的生产线对象。
	 */
	ProductionLine updateProductionLine(ProductionLine productionLine);

	/**
	 * 根据ID删除生产线。
	 * @param id 要删除的生产线的ID
	 */
	void deleteProductionLine(String id);

	/**
	 * 分页查询生产线。
	 * @param param 分页参数
	 * @return 返回分页后的生产线列表。
	 */
	PageList<ProductionLine> page(PageableParam<ProductionLine> param);

}
