package com.github.cret.web.oee.task;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.github.cret.web.oee.calculator.OeeCalculator;
import com.github.cret.web.oee.document.Workshop;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.AnalyzeResult;
import com.github.cret.web.oee.domain.analyze.OeeRawResult;
import com.github.cret.web.oee.domain.analyze.OeeResult;
import com.github.cret.web.oee.service.AnalyzeService;
import com.github.cret.web.oee.service.FileStorageService;
import com.github.cret.web.oee.service.TaskService;
import com.github.cret.web.oee.service.WorkshopService;
import com.github.cret.web.oee.utils.BuilderUtil;

/**
 * OEE导出异步任务处理类
 */
@Service
public class OeeExportTask {

	private static final Logger log = LoggerFactory.getLogger(OeeExportTask.class);

	private final AnalyzeService analyzeService;

	private final TaskService taskService;

	private final WorkshopService workshopService;

	private final FileStorageService fileStorageService;

	public OeeExportTask(AnalyzeService analyzeService, TaskService taskService, WorkshopService workshopService,
			FileStorageService fileStorageService) {
		this.analyzeService = analyzeService;
		this.taskService = taskService;
		this.workshopService = workshopService;
		this.fileStorageService = fileStorageService;
	}

	/**
	 * 异步执行OEE月度导出任务
	 * @param taskId 任务ID
	 * @param taskParams 任务参数（JSON格式）
	 */
	@Async("oeeCalculationExecutor")
	public void executeOeeMonthlyExport(String taskId, String workshopId, AnalyzeQuery query) {
		log.info("Starting OEE monthly export task: taskId={}", taskId);

		try {
			// 标记任务为处理中
			taskService.markTaskAsProcessing(taskId);

			// 更新进度：开始数据查询
			taskService.updateTaskProgress(taskId, 20);

			// 执行数据查询
			List<AnalyzeResult> analyzeResults = analyzeService.getWorkshopMonthlyOee(workshopId, query);

			List<OeeResult> oeeResults = calculateTotal(analyzeResults);

			// 更新进度：数据查询完成
			taskService.updateTaskProgress(taskId, 60);

			// 生成文件名
			String fileName = generateFileName(workshopId, query);
			String filePath = generateFilePath(fileName);

			// 更新进度：开始生成Excel文件
			taskService.updateTaskProgress(taskId, 80);

			// 生成Excel文件
			generateExcelFile(oeeResults, filePath);

			// 更新进度：文件生成完成
			taskService.updateTaskProgress(taskId, 100);

			// 标记任务为完成
			taskService.markTaskAsCompleted(taskId, filePath, fileName);

			log.info("OEE monthly export task completed successfully: taskId={}, fileName={}", taskId, fileName);

		}
		catch (Exception e) {
			log.error("OEE monthly export task failed: taskId={}", taskId, e);
			taskService.markTaskAsFailed(taskId, e.getMessage());
		}
	}

	/**
	 * 异步执行OEE日度导出任务
	 * @param taskId 任务ID
	 * @param taskParams 任务参数（JSON格式）
	 */
	@Async("oeeCalculationExecutor")
	public void executeOeeDailyExport(String taskId, String workshopId, AnalyzeQuery query) {
		log.info("Starting OEE daily export task: taskId={}", taskId);

		try {
			// 标记任务为处理中
			taskService.markTaskAsProcessing(taskId);

			// 更新进度：开始数据查询
			taskService.updateTaskProgress(taskId, 20);

			// 执行数据查询
			List<AnalyzeResult> analyzeResults = analyzeService.getWorkshopDailyOee(workshopId, query);

			List<OeeResult> oeeResults = calculateTotal(analyzeResults);

			// 更新进度：数据查询完成
			taskService.updateTaskProgress(taskId, 60);

			// 生成文件名
			String fileName = generateDailyFileName(workshopId, query);
			String filePath = generateFilePath(fileName);

			// 更新进度：开始生成Excel文件
			taskService.updateTaskProgress(taskId, 80);

			// 生成Excel文件
			generateExcelFile(oeeResults, filePath);

			// 更新进度：文件生成完成
			taskService.updateTaskProgress(taskId, 100);

			// 标记任务为完成
			taskService.markTaskAsCompleted(taskId, filePath, fileName);

			log.info("OEE daily export task completed successfully: taskId={}, fileName={}", taskId, fileName);

		}
		catch (Exception e) {
			log.error("OEE daily export task failed: taskId={}", taskId, e);
			taskService.markTaskAsFailed(taskId, e.getMessage());
		}
	}

	/**
	 * 生成月度导出文件名
	 */
	private String generateFileName(String workshopId, AnalyzeQuery query) {
		Workshop workshop = workshopService.findByCode(workshopId);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
		String monthStr = sdf.format(query.getStartTime());
		return String.format("%s%s月度OEE数据_%s.xlsx", workshop.getName(), monthStr, System.currentTimeMillis());
	}

	/**
	 * 生成日度导出文件名
	 */
	private String generateDailyFileName(String workshopId, AnalyzeQuery query) {
		Workshop workshop = workshopService.findByCode(workshopId);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String dateStr = sdf.format(query.getStartTime());
		return String.format("%s车间%s日度OEE数据_%s.xlsx", workshop.getName(), dateStr, System.currentTimeMillis());
	}

	/**
	 * 生成文件完整路径
	 */
	private String generateFilePath(String fileName) {
		return Paths.get(fileStorageService.getExportDirectory(), fileName).toString();
	}

	/**
	 * 生成Excel文件
	 */
	private void generateExcelFile(List<OeeResult> oeeResults, String filePath) throws IOException {
		// 确保导出目录存在
		fileStorageService.ensureExportDirectoryExists();

		File file = new File(filePath);
		try (FileOutputStream outputStream = new FileOutputStream(file)) {
			EasyExcel.write(outputStream, OeeResult.class).sheet("OEE指标").doWrite(oeeResults);
		}
		log.info("Excel file generated successfully: {}", filePath);
	}

	/**
	 * 计算汇总行
	 * @param inputList
	 * @return
	 */
	private List<OeeResult> calculateTotal(List<AnalyzeResult> inputList) {
		Double plannTime = 0.0;
		Double actualPlanTime = 0.0;
		Double runTime = 0.0;
		Double stopTime = 0.0;
		Integer changeoverNum = 0;
		Double changeoverTime = 0.0;
		Integer planBoard = 0;
		Integer actualBoard = 0;
		Integer planBoardPoints = 0;
		Integer actualBoardPoints = 0;
		Integer defectCount = 0;
		Integer firstPassDefectCount = 0;
		for (AnalyzeResult analyzeResult : inputList) {
			OeeRawResult oeeRawResult = analyzeResult.getOeeRawResult();
			plannTime += oeeRawResult.getPlanTime();
			actualPlanTime += oeeRawResult.getActualPlanTime();
			runTime += oeeRawResult.getRunTime();
			stopTime += oeeRawResult.getStopTime();
			changeoverNum += oeeRawResult.getChangeoverNum();
			changeoverTime += oeeRawResult.getChangeoverTime();
			planBoard += oeeRawResult.getPlanBoard();
			actualBoard += oeeRawResult.getActualBoard();
			planBoardPoints += oeeRawResult.getPlanBoardPoints();
			actualBoardPoints += oeeRawResult.getActualBoardPoints();
			defectCount += oeeRawResult.getDefectCount();
			firstPassDefectCount += oeeRawResult.getFirstPassDefectCount();
		}

		Double calculateAvailability = OeeCalculator.calculateAvailability(runTime, stopTime);
		Double calculatePerformance = OeeCalculator.calculatePerformance(actualBoard, planBoard);
		Double calculateQuality = actualBoard > 0 ? (double) (actualBoard - defectCount) / actualBoard : 0.00;
		Double calculateOee = OeeCalculator.calculateOee(calculatePerformance, calculateAvailability, calculateQuality);
		OeeResult oeeResult = BuilderUtil.builder(OeeResult::new)
			.with(OeeResult::setLineCode, "汇总")
			.with(OeeResult::setChangeoverNum, String.valueOf(changeoverNum))
			.with(OeeResult::setChangeoverTime, String.format("%.2f", changeoverTime / 3600))
			.with(OeeResult::setPlanTime, String.format("%.2f", plannTime / 3600))
			.with(OeeResult::setActualPlanTime, String.format("%.2f", actualPlanTime / 3600))
			.with(OeeResult::setRunTime, String.format("%.2f", runTime / 3600))
			.with(OeeResult::setStopTime, String.format("%.2f", stopTime / 3600))
			.with(OeeResult::setActualBoard, String.valueOf(actualBoard))
			.with(OeeResult::setPlanBoard, String.valueOf(planBoard))
			.with(OeeResult::setActualBoardPoints, String.valueOf(actualBoard))
			.with(OeeResult::setPlanBoardPoints, String.valueOf(planBoardPoints))
			.with(OeeResult::setFirstPassDefectCount, String.valueOf(firstPassDefectCount))
			.with(OeeResult::setDefectCount, String.valueOf(defectCount))
			.with(OeeResult::setAvailability, String.format("%.2f", calculateAvailability * 100))
			.with(OeeResult::setPerformance, String.format("%.2f", calculatePerformance * 100))
			.with(OeeResult::setQuality, String.format("%.2f", calculateQuality * 100))
			.with(OeeResult::setOee, String.format("%.2f", calculateOee * 100))
			.with(OeeResult::setAvailabilityTarget, String.format("%.2f", calculateAvailability * 100))
			.with(OeeResult::setPerformanceTarget, String.format("%.2f", calculatePerformance * 100))
			.with(OeeResult::setQualityTarget, String.format("%.2f", calculateQuality * 100))
			.with(OeeResult::setOeeTarget, String.format("%.2f", calculateOee * 100))
			.build();

		List<OeeResult> result = inputList.stream().map(AnalyzeResult::getOeeResult).collect(Collectors.toList());
		result.add(oeeResult);
		return result;
	}

}
