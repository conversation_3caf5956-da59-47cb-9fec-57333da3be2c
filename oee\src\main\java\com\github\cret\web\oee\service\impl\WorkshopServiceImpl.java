package com.github.cret.web.oee.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.github.cret.web.oee.document.Workshop;
import com.github.cret.web.oee.domain.workshop.WorkshopTree;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import com.github.cret.web.oee.repository.WorkshopRepository;
import com.github.cret.web.oee.service.WorkshopService;

@Service
public class WorkshopServiceImpl implements WorkshopService {

	private final WorkshopRepository repository;

	public WorkshopServiceImpl(WorkshopRepository repository) {
		this.repository = repository;
	}

	@Override
	public List<Workshop> getList() {
		return repository.findAll();
	}

	@Override
	public Workshop saveWorkshop(Workshop workshop) {
		return repository.save(workshop);
	}

	@Override
	public void deleteWorkshop(String id) {
		repository.deleteById(id);
	}

	@Override
	public Workshop getWorkshopById(String id) {
		return repository.findById(id).orElse(null);
	}

	@Override
	public Workshop findByCode(String code) {
		return repository.findByCode(code);
	}

	@Override
	public List<WorkshopTree> getWorkshopTree() {
		List<Workshop> workshops = repository.findAll();
		Map<String, WorkshopTree> workshopMap = new HashMap<>();
		List<WorkshopTree> rootNodes = new ArrayList<>();

		for (Workshop workshop : workshops) {
			WorkshopTree treeNode = new WorkshopTree();
			treeNode.setId(workshop.getId());
			treeNode.setCode(workshop.getCode());
			treeNode.setName(workshop.getName());
			treeNode.setChildren(new ArrayList<>());
			workshopMap.put(workshop.getId(), treeNode);
		}

		for (Workshop workshop : workshops) {
			WorkshopTree treeNode = workshopMap.get(workshop.getId());
			String parentId = workshop.getParentId();

			if (parentId == null || parentId.isEmpty()) {
				rootNodes.add(treeNode);
			}
			else {
				WorkshopTree parentNode = workshopMap.get(parentId);
				if (parentNode != null) {
					parentNode.getChildren().add(treeNode);
				}
				else {
					rootNodes.add(treeNode);
				}
			}
		}

		return rootNodes;
	}

	@Override
	public List<Workshop> getLeafWorkshopsByCode(String workshopCode) {
		Workshop workshop = repository.findByCode(workshopCode);
		if (workshop == null) {
			return new ArrayList<>();
		}
		List<Workshop> allWorkshops = repository.findAll();
		Map<String, List<Workshop>> parentIdToChildrenMap = new HashMap<>();
		for (Workshop ws : allWorkshops) {
			parentIdToChildrenMap.computeIfAbsent(ws.getParentId(), k -> new ArrayList<>()).add(ws);
		}
		List<Workshop> leafWorkshops = new ArrayList<>();
		findLeafWorkshops(workshop, parentIdToChildrenMap, leafWorkshops);
		return leafWorkshops;
	}

	private void findLeafWorkshops(Workshop workshop, Map<String, List<Workshop>> parentIdToChildrenMap,
			List<Workshop> leafWorkshops) {
		List<Workshop> children = parentIdToChildrenMap.get(workshop.getId());
		if (children == null || children.isEmpty()) {
			leafWorkshops.add(workshop);
		}
		else {
			for (Workshop child : children) {
				findLeafWorkshops(child, parentIdToChildrenMap, leafWorkshops);
			}
		}
	}

	@Override
	public List<Workshop> getAllLeafWorkshops() {
		List<Workshop> allWorkshops = repository.findAll();
		Map<String, List<Workshop>> parentIdToChildrenMap = new HashMap<>();
		for (Workshop ws : allWorkshops) {
			if (ws.getParentId() != null) {
				parentIdToChildrenMap.computeIfAbsent(ws.getParentId(), k -> new ArrayList<>()).add(ws);
			}
		}
		List<Workshop> leafWorkshops = new ArrayList<>();
		for (Workshop workshop : allWorkshops) {
			if (!parentIdToChildrenMap.containsKey(workshop.getId())) {
				leafWorkshops.add(workshop);
			}
		}
		return leafWorkshops;
	}

}
