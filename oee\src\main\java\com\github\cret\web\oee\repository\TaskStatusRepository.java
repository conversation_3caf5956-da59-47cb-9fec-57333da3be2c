package com.github.cret.web.oee.repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.github.cret.web.oee.document.TaskStatus;

/**
 * 任务状态数据访问接口
 */
@Repository
public interface TaskStatusRepository extends MongoRepository<TaskStatus, String> {

	/**
	 * 根据任务ID查找任务状态
	 */
	Optional<TaskStatus> findByTaskId(String taskId);

	/**
	 * 根据用户ID查找任务列表
	 */
	List<TaskStatus> findByUserIdOrderByCreatedAtDesc(String userId);

	/**
	 * 根据任务状态查找任务列表
	 */
	List<TaskStatus> findByStatus(TaskStatus.TaskStatusEnum status);

	/**
	 * 查找指定时间之前创建的已完成任务（用于清理）
	 */
	List<TaskStatus> findByStatusAndCreatedAtBefore(TaskStatus.TaskStatusEnum status, Date date);

	/**
	 * 根据任务ID和用户ID查找任务（用于权限验证）
	 */
	Optional<TaskStatus> findByTaskIdAndUserId(String taskId, String userId);

	/**
	 * 删除指定时间之前创建的任务
	 */
	void deleteByCreatedAtBefore(Date date);

}
