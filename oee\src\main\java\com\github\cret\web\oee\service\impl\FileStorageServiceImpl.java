package com.github.cret.web.oee.service.impl;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.github.cret.web.oee.service.FileStorageService;

/**
 * 文件存储服务实现类
 */
@Service
public class FileStorageServiceImpl implements FileStorageService {

	private static final Logger log = LoggerFactory.getLogger(FileStorageServiceImpl.class);

	// 导出文件存储目录
	private static final String EXPORT_DIR = "exports";

	public FileStorageServiceImpl() {
		// 初始化时确保导出目录存在
		ensureExportDirectoryExists();
	}

	@Override
	public String getExportDirectory() {
		return EXPORT_DIR;
	}

	@Override
	public void ensureExportDirectoryExists() {
		try {
			Path exportPath = Paths.get(EXPORT_DIR);
			if (!Files.exists(exportPath)) {
				Files.createDirectories(exportPath);
				log.info("Created export directory: {}", EXPORT_DIR);
			}
		}
		catch (IOException e) {
			log.error("Failed to create export directory: {}", EXPORT_DIR, e);
			throw new RuntimeException("无法创建导出目录", e);
		}
	}

	@Override
	public boolean fileExists(String filePath) {
		return Files.exists(Paths.get(filePath));
	}

	@Override
	public File getFile(String filePath) {
		return new File(filePath);
	}

	@Override
	public boolean deleteFile(String filePath) {
		try {
			boolean deleted = Files.deleteIfExists(Paths.get(filePath));
			if (deleted) {
				log.info("Deleted file: {}", filePath);
			}
			return deleted;
		}
		catch (IOException e) {
			log.error("Failed to delete file: {}", filePath, e);
			return false;
		}
	}

	@Override
	public int cleanupExpiredFiles(int daysToKeep) {
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_MONTH, -daysToKeep);
		Date cutoffDate = calendar.getTime();
		long cutoffTime = cutoffDate.getTime();

		List<File> files = listFiles();
		int deletedCount = 0;

		for (File file : files) {
			if (file.lastModified() < cutoffTime) {
				if (deleteFile(file.getAbsolutePath())) {
					deletedCount++;
				}
			}
		}

		if (deletedCount > 0) {
			log.info("Cleaned up {} expired files older than {} days", deletedCount, daysToKeep);
		}

		return deletedCount;
	}

	@Override
	public List<File> listFiles() {
		File exportDir = new File(EXPORT_DIR);
		if (!exportDir.exists() || !exportDir.isDirectory()) {
			return Collections.emptyList();
		}

		File[] files = exportDir.listFiles();
		if (files == null) {
			return Collections.emptyList();
		}

		return Arrays.asList(files);
	}

	@Override
	public long getFileSize(String filePath) {
		try {
			return Files.size(Paths.get(filePath));
		}
		catch (IOException e) {
			log.error("Failed to get file size: {}", filePath, e);
			return 0;
		}
	}

	@Override
	public long getDirectorySize() {
		List<File> files = listFiles();
		long totalSize = 0;

		for (File file : files) {
			if (file.isFile()) {
				totalSize += file.length();
			}
		}

		return totalSize;
	}

}
