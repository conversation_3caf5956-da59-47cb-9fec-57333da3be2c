package com.github.cret.web.oee.document;

import java.util.Date;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 任务状态实体类 用于追踪异步导出任务的执行状态
 */
@Document(collection = "t_task_status")
public class TaskStatus {

	@Id
	private String taskId;

	/**
	 * 用户ID，用于权限控制
	 */
	private String userId;

	/**
	 * 任务类型（如：OEE_EXPORT）
	 */
	private String taskType;

	/**
	 * 任务状态：PENDING, PROCESSING, COMPLETED, FAILED
	 */
	private TaskStatusEnum status;

	/**
	 * 任务进度（0-100）
	 */
	private Integer progress;

	/**
	 * 生成的文件路径
	 */
	private String filePath;

	/**
	 * 文件名
	 */
	private String fileName;

	/**
	 * 错误信息（任务失败时记录）
	 */
	private String errorMessage;

	/**
	 * 任务参数（JSON格式存储）
	 */
	private String taskParams;

	/**
	 * 创建时间
	 */
	private Date createdAt;

	/**
	 * 完成时间
	 */
	private Date finishedAt;

	/**
	 * 任务状态枚举
	 */
	public enum TaskStatusEnum {

		PENDING, // 等待中
		PROCESSING, // 处理中
		COMPLETED, // 已完成
		FAILED // 失败

	}

	public TaskStatus() {
		// this.createdAt = new Date();
		// this.status = TaskStatusEnum.PENDING;
		// this.progress = 0;
	}

	public TaskStatus(String taskId, String userId, String taskType) {
		this();
		this.taskId = taskId;
		this.userId = userId;
		this.taskType = taskType;
	}

	// Getters and Setters
	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getTaskType() {
		return taskType;
	}

	public void setTaskType(String taskType) {
		this.taskType = taskType;
	}

	public TaskStatusEnum getStatus() {
		return status;
	}

	public void setStatus(TaskStatusEnum status) {
		this.status = status;
	}

	public Integer getProgress() {
		return progress;
	}

	public void setProgress(Integer progress) {
		this.progress = progress;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public String getTaskParams() {
		return taskParams;
	}

	public void setTaskParams(String taskParams) {
		this.taskParams = taskParams;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getFinishedAt() {
		return finishedAt;
	}

	public void setFinishedAt(Date finishedAt) {
		this.finishedAt = finishedAt;
	}

	/**
	 * 标记任务为处理中
	 */
	public void markAsProcessing() {
		this.status = TaskStatusEnum.PROCESSING;
		this.progress = 0;
	}

	/**
	 * 更新任务进度
	 */
	public void updateProgress(int progress) {
		this.progress = Math.max(0, Math.min(100, progress));
	}

	/**
	 * 标记任务为完成
	 */
	public void markAsCompleted(String filePath, String fileName) {
		this.status = TaskStatusEnum.COMPLETED;
		this.progress = 100;
		this.filePath = filePath;
		this.fileName = fileName;
		this.finishedAt = new Date();
	}

	/**
	 * 标记任务为失败
	 */
	public void markAsFailed(String errorMessage) {
		this.status = TaskStatusEnum.FAILED;
		this.errorMessage = errorMessage;
		this.finishedAt = new Date();
	}

}
