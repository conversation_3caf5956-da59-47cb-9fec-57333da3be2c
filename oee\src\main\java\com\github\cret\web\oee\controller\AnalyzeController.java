package com.github.cret.web.oee.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.excel.EasyExcel;
import com.github.cret.web.common.util.JacksonUtil;
import com.github.cret.web.oee.document.TaskStatus;
import com.github.cret.web.oee.document.Workshop;
import com.github.cret.web.oee.domain.analyze.AlarmInfo;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.AnalyzeResult;
import com.github.cret.web.oee.domain.analyze.HourlyOutputList;
import com.github.cret.web.oee.domain.analyze.OeeResult;
import com.github.cret.web.oee.domain.export.ExportTaskRequest;
import com.github.cret.web.oee.domain.export.ExportTaskResponse;
import com.github.cret.web.oee.domain.export.TaskStatusResponse;
import com.github.cret.web.oee.service.AnalyzeService;
import com.github.cret.web.oee.service.TaskService;
import com.github.cret.web.oee.service.WorkshopService;
import com.github.cret.web.oee.task.OeeExportTask;
import com.github.cret.web.oee.utils.BuilderUtil;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 分析控制器
 */
@RestController
@RequestMapping("/analyze")
public class AnalyzeController {

	private final AnalyzeService analyzeService;

	private final WorkshopService workshopService;

	private final TaskService taskService;

	private final OeeExportTask oeeExportTask;

	public AnalyzeController(AnalyzeService analyzeService, WorkshopService workshopService, TaskService taskService,
			OeeExportTask oeeExportTask) {
		this.analyzeService = analyzeService;
		this.workshopService = workshopService;
		this.taskService = taskService;
		this.oeeExportTask = oeeExportTask;
	}

	/**
	 * 获取线体OEE等指标
	 * @param query 包含线体编码、开始时间和结束时间的查询条件
	 * @return OEE等指标结果
	 */
	@PostMapping("/line-oee")
	public AnalyzeResult getLineOee(@RequestBody AnalyzeQuery query) {
		return analyzeService.getLineOee(query);
	}

	/**
	 * 获取线体每小时产出数据
	 * @param query 包含线体编码、开始时间和结束时间的查询条件
	 * @return 每小时产出数据列表
	 */
	@PostMapping("/hourly-output")
	public HourlyOutputList getHourlyOutput(@RequestBody AnalyzeQuery query) {
		return analyzeService.getHourlyOutput(query);
	}

	/**
	 * 获取Top5异常统计数据
	 * @param query 包含线体编码、开始时间和结束时间的查询条件
	 * @return Top5异常统计列表
	 */
	@PostMapping("/top-alarms")
	public List<AlarmInfo> getTopAlarms(@RequestBody AnalyzeQuery query) {
		return analyzeService.getTopAlarms(query);
	}

	/**
	 * 获取服务器当前时间（上海时区 UTC+8）
	 * @return 服务器时间戳（毫秒）
	 */
	@PostMapping("/server-time")
	public Long getServerTime() {
		ZoneId zoneId = ZoneId.of("Asia/Shanghai");
		LocalDateTime dateTime = LocalDateTime.now(zoneId);
		return dateTime.atZone(zoneId).toInstant().toEpochMilli();
	}

	/**
	 * 月度oee指标查询
	 * @param workShopId
	 * @param query
	 * @return
	 */
	@PostMapping("/workshop-monthly-oee/{workShopId}")
	public List<AnalyzeResult> getWorkshopMonthlyOee(@PathVariable String workShopId, @RequestBody AnalyzeQuery query) {
		return analyzeService.getWorkshopMonthlyOee(workShopId, query);
	}

	/**
	 * @param response
	 * @param param
	 * @throws IOException
	 */
	@PostMapping("/export/{workShopId}")
	public void exportExcel(HttpServletResponse response, @PathVariable String workShopId,
			@RequestBody AnalyzeQuery query) throws IOException {
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode(workShopId + "车间数据", StandardCharsets.UTF_8);
		response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

		List<AnalyzeResult> oeeResults = analyzeService.getWorkshopMonthlyOee(workShopId, query);
		EasyExcel.write(response.getOutputStream(), OeeResult.class).sheet("oee指标").doWrite(oeeResults);
	}

	/**
	 * @param response
	 * @param param
	 * @throws IOException
	 */
	@PostMapping("/export/daily/{workShopId}")
	public void exportDailyExcel(HttpServletResponse response, @PathVariable String workShopId,
			@RequestBody AnalyzeQuery query) throws IOException {
		Workshop workshop = workshopService.findByCode(workShopId);
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode(workshop.getName() + "车间数据", StandardCharsets.UTF_8);
		response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

		List<AnalyzeResult> oeeResults = analyzeService.getWorkshopDailyOee(workShopId, query);
		EasyExcel.write(response.getOutputStream(), OeeResult.class).sheet("oee指标").doWrite(oeeResults);
	}

	/**
	 * 创建OEE月度导出任务
	 * @param request 导出任务请求
	 * @return 任务响应
	 */
	@PostMapping("/export-task/monthly")
	public ResponseEntity<ExportTaskResponse> createMonthlyExportTask(@RequestBody ExportTaskRequest request) {
		try {
			// 创建任务
			TaskStatus task = taskService.createTask("system", "OEE_MONTHLY_EXPORT",
					JacksonUtil.getObjectMapper().writeValueAsString(request));

			AnalyzeQuery analyzeQuery = BuilderUtil.builder(AnalyzeQuery::new)
				.with(AnalyzeQuery::setStartTime, request.getStartTime())
				.with(AnalyzeQuery::setEndTime, request.getEndTime())
				.build();

			// 异步执行导出任务
			oeeExportTask.executeOeeMonthlyExport(task.getTaskId(), request.getWorkshopId(), analyzeQuery);

			return ResponseEntity.accepted().body(new ExportTaskResponse(task.getTaskId()));
		}
		catch (Exception e) {
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
				.body(new ExportTaskResponse(null, "创建导出任务失败: " + e.getMessage()));
		}
	}

	/**
	 * 创建OEE日度导出任务
	 * @param request 导出任务请求
	 * @return 任务响应
	 */
	@PostMapping("/export-task/daily")
	public ResponseEntity<ExportTaskResponse> createDailyExportTask(@RequestBody ExportTaskRequest request) {
		try {
			// 创建任务
			TaskStatus task = taskService.createTask("system", "OEE_DAILY_EXPORT",
					JacksonUtil.getObjectMapper().writeValueAsString(request));

			AnalyzeQuery analyzeQuery = BuilderUtil.builder(AnalyzeQuery::new)
				.with(AnalyzeQuery::setStartTime, request.getStartTime())
				.with(AnalyzeQuery::setEndTime, request.getEndTime())
				.build();

			// 异步执行导出任务
			oeeExportTask.executeOeeDailyExport(task.getTaskId(), request.getWorkshopId(), analyzeQuery);

			return ResponseEntity.accepted().body(new ExportTaskResponse(task.getTaskId()));
		}
		catch (Exception e) {
			return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
				.body(new ExportTaskResponse(null, "创建导出任务失败: " + e.getMessage()));
		}
	}

	/**
	 * 查询任务状态
	 * @param taskId 任务ID
	 * @return 任务状态响应
	 */
	@GetMapping("/export-task/{taskId}/status")
	public ResponseEntity<TaskStatusResponse> getTaskStatus(@PathVariable String taskId) {
		Optional<TaskStatus> taskOpt = taskService.getTaskStatus(taskId);
		if (taskOpt.isPresent()) {
			return ResponseEntity.ok(new TaskStatusResponse(taskOpt.get()));
		}
		else {
			return ResponseEntity.notFound().build();
		}
	}

	/**
	 * 下载导出文件
	 * @param taskId 任务ID
	 * @param response HTTP响应
	 * @throws IOException IO异常
	 */
	@GetMapping("/export-task/{taskId}/download")
	public void downloadExportFile(@PathVariable String taskId, HttpServletResponse response) throws IOException {
		Optional<TaskStatus> taskOpt = taskService.getTaskStatus(taskId);

		if (!taskOpt.isPresent()) {
			response.setStatus(HttpStatus.NOT_FOUND.value());
			response.getWriter().write("任务不存在");
			return;
		}

		TaskStatus task = taskOpt.get();

		if (task.getStatus() != TaskStatus.TaskStatusEnum.COMPLETED) {
			response.setStatus(HttpStatus.BAD_REQUEST.value());
			response.getWriter().write("任务尚未完成");
			return;
		}

		File file = new File(task.getFilePath());
		if (!file.exists()) {
			response.setStatus(HttpStatus.NOT_FOUND.value());
			response.getWriter().write("文件不存在");
			return;
		}

		// 设置响应头
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode(task.getFileName(), StandardCharsets.UTF_8);
		response.setHeader("Content-disposition", "attachment;filename=" + fileName);

		// 输出文件
		try (FileInputStream inputStream = new FileInputStream(file)) {
			inputStream.transferTo(response.getOutputStream());
		}
	}

}
