package com.github.cret.web.oee.domain;

import java.util.Date;

import com.github.cret.web.oee.document.TaskStatus.TaskStatusEnum;

/**
 * 任务状态查询参数
 */
public class TaskStatusQuery {

	/**
	 * 用户ID
	 */
	private String userId;

	/**
	 * 任务类型
	 */
	private String taskType;

	/**
	 * 任务状态
	 */
	private TaskStatusEnum status;

	/**
	 * 创建时间开始
	 */
	private Date createdAtStart;

	/**
	 * 创建时间结束
	 */
	private Date createdAtEnd;

	/**
	 * 完成时间开始
	 */
	private Date finishedAtStart;

	/**
	 * 完成时间结束
	 */
	private Date finishedAtEnd;

	public TaskStatusQuery() {
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getTaskType() {
		return taskType;
	}

	public void setTaskType(String taskType) {
		this.taskType = taskType;
	}

	public TaskStatusEnum getStatus() {
		return status;
	}

	public void setStatus(TaskStatusEnum status) {
		this.status = status;
	}

	public Date getCreatedAtStart() {
		return createdAtStart;
	}

	public void setCreatedAtStart(Date createdAtStart) {
		this.createdAtStart = createdAtStart;
	}

	public Date getCreatedAtEnd() {
		return createdAtEnd;
	}

	public void setCreatedAtEnd(Date createdAtEnd) {
		this.createdAtEnd = createdAtEnd;
	}

	public Date getFinishedAtStart() {
		return finishedAtStart;
	}

	public void setFinishedAtStart(Date finishedAtStart) {
		this.finishedAtStart = finishedAtStart;
	}

	public Date getFinishedAtEnd() {
		return finishedAtEnd;
	}

	public void setFinishedAtEnd(Date finishedAtEnd) {
		this.finishedAtEnd = finishedAtEnd;
	}

}
