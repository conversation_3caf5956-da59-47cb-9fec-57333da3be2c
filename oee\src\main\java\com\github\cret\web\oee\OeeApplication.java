package com.github.cret.web.oee;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableAspectJAutoProxy
@ComponentScan(basePackages = "com.github.cret.web")
@EnableMongoRepositories(basePackages = "com.github.cret.web")
@EnableScheduling
@EnableAsync
public class OeeApplication {

	public static void main(String[] args) {
		SpringApplication.run(OeeApplication.class, args);
	}

}
