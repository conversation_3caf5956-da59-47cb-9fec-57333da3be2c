package com.github.cret.web.oee.service;

import java.util.List;
import java.util.Optional;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.TaskStatus;
import com.github.cret.web.oee.domain.TaskStatusQuery;

/**
 * 任务管理服务接口
 */
public interface TaskService {

	/**
	 * 创建新任务
	 * @param userId 用户ID
	 * @param taskType 任务类型
	 * @param taskParams 任务参数（JSON格式）
	 * @return 任务状态对象
	 */
	TaskStatus createTask(String userId, String taskType, String taskParams);

	/**
	 * 根据任务ID获取任务状态
	 * @param taskId 任务ID
	 * @return 任务状态对象
	 */
	Optional<TaskStatus> getTaskStatus(String taskId);

	/**
	 * 根据任务ID和用户ID获取任务状态（权限验证）
	 * @param taskId 任务ID
	 * @param userId 用户ID
	 * @return 任务状态对象
	 */
	Optional<TaskStatus> getTaskStatus(String taskId, String userId);

	/**
	 * 更新任务状态
	 * @param taskStatus 任务状态对象
	 * @return 更新后的任务状态对象
	 */
	TaskStatus updateTaskStatus(TaskStatus taskStatus);

	/**
	 * 标记任务为处理中
	 * @param taskId 任务ID
	 */
	void markTaskAsProcessing(String taskId);

	/**
	 * 更新任务进度
	 * @param taskId 任务ID
	 * @param progress 进度（0-100）
	 */
	void updateTaskProgress(String taskId, int progress);

	/**
	 * 标记任务为完成
	 * @param taskId 任务ID
	 * @param filePath 文件路径
	 * @param fileName 文件名
	 */
	void markTaskAsCompleted(String taskId, String filePath, String fileName);

	/**
	 * 标记任务为失败
	 * @param taskId 任务ID
	 * @param errorMessage 错误信息
	 */
	void markTaskAsFailed(String taskId, String errorMessage);

	/**
	 * 获取用户的任务列表
	 * @param userId 用户ID
	 * @return 任务状态列表
	 */
	List<TaskStatus> getUserTasks(String userId);

	/**
	 * 清理过期的已完成任务
	 * @param daysToKeep 保留天数
	 * @return 清理的任务数量
	 */
	int cleanupExpiredTasks(int daysToKeep);

	/**
	 * 生成唯一的任务ID
	 * @return 任务ID
	 */
	String generateTaskId();

	/**
	 * 分页查询任务状态
	 * @param param 查询参数
	 * @return 分页结果
	 */
	PageList<TaskStatus> page(PageableParam<TaskStatus> param);

	/**
	 * 分页查询任务状态（支持复杂查询条件）
	 * @param param 查询参数
	 * @return 分页结果
	 */
	PageList<TaskStatus> pageWithQuery(PageableParam<TaskStatusQuery> param);

}
