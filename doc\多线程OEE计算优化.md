# getWorkshopMonthlyOee方法多线程优化

## 概述
将 `getWorkshopMonthlyOee` 方法从单线程串行处理优化为使用自定义线程池的多线程并行处理，以提高车间月度OEE计算的性能。

## 修改内容

### 1. 线程池配置 (SchedulingConfig.java)
在现有的调度配置类中添加了专用于OEE计算的线程池：

```java
@Bean("oeeCalculationExecutor")
public ThreadPoolTaskExecutor oeeCalculationExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    // 核心线程数：CPU核心数
    executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
    // 最大线程数：CPU核心数的2倍
    executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
    // 队列容量
    executor.setQueueCapacity(100);
    // 线程名前缀
    executor.setThreadNamePrefix("OEE-Calc-");
    // 空闲线程存活时间
    executor.setKeepAliveSeconds(60);
    // 拒绝策略：调用者运行
    executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    // 等待所有任务完成后再关闭线程池
    executor.setWaitForTasksToCompleteOnShutdown(true);
    // 等待时间
    executor.setAwaitTerminationSeconds(60);
    executor.initialize();
    return executor;
}
```

### 2. 服务类修改 (AnalyzeServiceImpl.java)

#### 2.1 添加依赖注入
- 添加了 `ThreadPoolTaskExecutor` 字段
- 在构造函数中注入自定义线程池
- 使用 `@Qualifier("oeeCalculationExecutor")` 指定具体的线程池Bean

#### 2.2 方法重构
将原来的串行处理：
```java
return produtionLineByWorkshopCode.stream().map(line -> {
    AnalyzeQuery lineQuery = new AnalyzeQuery();
    lineQuery.setCode(line.getCode());
    lineQuery.setStartTime(query.getStartTime());
    lineQuery.setEndTime(query.getEndTime());
    return getLineOee(lineQuery).getOeeResult();
}).toList();
```

改为并行处理：
```java
// 使用自定义线程池并行计算每条线体的OEE
List<CompletableFuture<OeeResult>> futures = produtionLineByWorkshopCode.stream()
    .map(line -> CompletableFuture.supplyAsync(() -> {
        AnalyzeQuery lineQuery = new AnalyzeQuery();
        lineQuery.setCode(line.getCode());
        lineQuery.setStartTime(query.getStartTime());
        lineQuery.setEndTime(query.getEndTime());
        return getLineOee(lineQuery).getOeeResult();
    }, oeeCalculationExecutor))
    .toList();

// 等待所有任务完成并收集结果
return futures.stream()
    .map(CompletableFuture::join)
    .toList();
```

## 性能优势

### 1. 并行处理
- **原来**：串行处理每条生产线的OEE计算，总时间 = 单线体计算时间 × 线体数量
- **现在**：并行处理多条生产线的OEE计算，总时间 ≈ 最慢的单线体计算时间

### 2. 资源利用
- 充分利用多核CPU资源
- 根据CPU核心数动态调整线程池大小
- 合理的队列容量避免内存溢出

### 3. 可控性
- 使用自定义线程池，避免与其他异步任务竞争资源
- 配置了合适的拒绝策略，确保系统稳定性
- 优雅关闭机制，确保应用停止时任务能正常完成

## 线程池参数说明

| 参数 | 值 | 说明 |
|------|----|----- |
| corePoolSize | CPU核心数 | 核心线程数，常驻线程 |
| maxPoolSize | CPU核心数×2 | 最大线程数，高负载时扩展 |
| queueCapacity | 100 | 任务队列容量 |
| keepAliveSeconds | 60 | 空闲线程存活时间 |
| rejectedExecutionHandler | CallerRunsPolicy | 拒绝策略：调用者线程执行 |

## 注意事项

1. **线程安全**：确保 `getLineOee` 方法及其依赖的服务都是线程安全的
2. **资源管理**：线程池会在应用关闭时自动清理，无需手动管理
3. **异常处理**：使用 `CompletableFuture.join()` 会传播异常，保持原有的异常处理逻辑
4. **监控**：可以通过线程名前缀 "OEE-Calc-" 在日志中识别相关线程

## 测试建议

1. 单元测试：验证多线程逻辑正确性
2. 性能测试：对比优化前后的执行时间
3. 压力测试：验证高并发场景下的稳定性
4. 监控测试：观察线程池使用情况和资源消耗
