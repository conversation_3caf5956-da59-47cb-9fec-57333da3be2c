# TaskStatus 分页接口使用说明

## 概述

TaskStatus 类现在支持分页查询功能，提供了两种分页接口：
1. 基础分页接口：使用 TaskStatus 实体作为查询条件
2. 高级分页接口：使用 TaskStatusQuery 作为查询条件，支持更复杂的查询场景

## API 接口

### 1. 基础分页查询

**接口地址：** `POST /task-status/page`

**请求参数：**
```json
{
  "pageData": {
    "pageNumber": 0,
    "pageSize": 10,
    "sortField": "createdAt",
    "sortDirection": "DESC"
  },
  "searchParams": {
    "userId": "user123",
    "taskType": "OEE_EXPORT",
    "status": "COMPLETED"
  }
}
```

**响应格式：**
```json
{
  "total": 25,
  "hasNext": true,
  "list": [
    {
      "taskId": "abc123",
      "userId": "user123",
      "taskType": "OEE_EXPORT",
      "status": "COMPLETED",
      "progress": 100,
      "filePath": "/exports/file.xlsx",
      "fileName": "oee_report_2024.xlsx",
      "errorMessage": null,
      "taskParams": "{\"startDate\":\"2024-01-01\"}",
      "createdAt": "2024-01-15T10:30:00Z",
      "finishedAt": "2024-01-15T10:35:00Z"
    }
  ]
}
```

### 2. 高级分页查询

**接口地址：** `POST /task-status/page-query`

**请求参数：**
```json
{
  "pageData": {
    "pageNumber": 0,
    "pageSize": 10,
    "sortField": "createdAt",
    "sortDirection": "DESC"
  },
  "searchParams": {
    "userId": "user123",
    "taskType": "EXPORT",
    "status": "COMPLETED",
    "createdAtStart": "2024-01-01T00:00:00Z",
    "createdAtEnd": "2024-01-31T23:59:59Z",
    "finishedAtStart": "2024-01-01T00:00:00Z",
    "finishedAtEnd": "2024-01-31T23:59:59Z"
  }
}
```

## 查询参数说明

### PageData 参数
- `pageNumber`: 页码，从0开始
- `pageSize`: 每页大小，默认10
- `sortField`: 排序字段，可选值：taskId, userId, taskType, status, createdAt, finishedAt
- `sortDirection`: 排序方向，ASC（升序）或 DESC（降序）

### TaskStatus 查询参数（基础查询）
- `userId`: 用户ID（精确匹配）
- `taskType`: 任务类型（包含匹配）
- `status`: 任务状态（精确匹配）

### TaskStatusQuery 查询参数（高级查询）
- `userId`: 用户ID（精确匹配）
- `taskType`: 任务类型（正则匹配，不区分大小写）
- `status`: 任务状态（精确匹配）
- `createdAtStart`: 创建时间开始
- `createdAtEnd`: 创建时间结束
- `finishedAtStart`: 完成时间开始
- `finishedAtEnd`: 完成时间结束

## 任务状态枚举

```java
public enum TaskStatusEnum {
    PENDING,    // 等待中
    PROCESSING, // 处理中
    COMPLETED,  // 已完成
    FAILED      // 失败
}
```

## 使用示例

### 查询用户的所有已完成任务
```json
{
  "pageData": {
    "pageNumber": 0,
    "pageSize": 20,
    "sortField": "finishedAt",
    "sortDirection": "DESC"
  },
  "searchParams": {
    "userId": "user123",
    "status": "COMPLETED"
  }
}
```

### 查询指定时间范围内的导出任务
```json
{
  "pageData": {
    "pageNumber": 0,
    "pageSize": 10
  },
  "searchParams": {
    "taskType": "EXPORT",
    "createdAtStart": "2024-01-01T00:00:00Z",
    "createdAtEnd": "2024-01-31T23:59:59Z"
  }
}
```

## 其他相关接口

### 获取单个任务状态
**接口地址：** `GET /task-status/{taskId}`

### 获取用户任务列表
**接口地址：** `GET /task-status/user/{userId}`

## 注意事项

1. 时间参数使用 ISO 8601 格式
2. 分页从第0页开始
3. 高级查询接口支持时间范围查询，可以只指定开始时间或结束时间
4. 任务类型支持模糊匹配（高级查询）或包含匹配（基础查询）
5. 所有查询参数都是可选的，不指定则不作为查询条件
