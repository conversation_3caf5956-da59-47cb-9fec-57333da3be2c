package com.github.cret.web.oee.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.TaskStatus;
import com.github.cret.web.oee.domain.TaskStatusQuery;
import com.github.cret.web.oee.service.TaskService;

/**
 * 任务状态控制器
 */
@RestController
@RequestMapping("/task-status")
public class TaskStatusController {

	private final TaskService taskService;

	public TaskStatusController(TaskService taskService) {
		this.taskService = taskService;
	}

	/**
	 * 分页查询任务状态
	 * @param param 查询参数
	 * @return 分页结果
	 */
	@PostMapping("/page")
	public PageList<TaskStatus> page(@RequestBody PageableParam<TaskStatus> param) {
		return taskService.page(param);
	}

	/**
	 * 分页查询任务状态（支持复杂查询条件）
	 * @param param 查询参数
	 * @return 分页结果
	 */
	@PostMapping("/page-query")
	public PageList<TaskStatus> pageWithQuery(@RequestBody PageableParam<TaskStatusQuery> param) {
		return taskService.pageWithQuery(param);
	}

	/**
	 * 根据任务ID获取任务状态
	 * @param taskId 任务ID
	 * @return 任务状态
	 */
	@GetMapping("/{taskId}")
	public Optional<TaskStatus> getTaskStatus(@PathVariable String taskId) {
		return taskService.getTaskStatus(taskId);
	}

	/**
	 * 获取用户的任务列表
	 * @param userId 用户ID
	 * @return 任务状态列表
	 */
	@GetMapping("/user/{userId}")
	public List<TaskStatus> getUserTasks(@PathVariable String userId) {
		return taskService.getUserTasks(userId);
	}

}
