package com.github.cret.web.oee.controller;

import java.util.List;
import org.springframework.web.bind.annotation.*;
import com.github.cret.web.oee.document.Workshop;
import com.github.cret.web.oee.domain.workshop.WorkshopTree;
import com.github.cret.web.oee.service.WorkshopService;

@RestController
@RequestMapping("/workshop")
public class WorkshopController {

	private final WorkshopService workshopService;

	public WorkshopController(WorkshopService workshopService) {
		this.workshopService = workshopService;
	}

	/**
	 * 获取车间列表
	 * @return 车间列表
	 */

	@GetMapping("/list")
	public List<Workshop> getAllWorkshops() {
		return workshopService.getList();
	}

	/**
	 * 创建车间
	 * @param workshop 车间
	 * @return 车间
	 */
	@PostMapping
	public Workshop createWorkshop(@RequestBody Workshop workshop) {
		return workshopService.saveWorkshop(workshop);
	}

	/**
	 * 获取车间树结构
	 * @return
	 */
	@GetMapping("/tree")
	public List<WorkshopTree> getWorkshopTree() {
		return workshopService.getWorkshopTree();
	}

	/**
	 * 根据车间编码获取所有叶子节点
	 * @param workshopCode 车间编码
	 * @return 叶子节点列表
	 */
	@GetMapping("/leaf-nodes")
	public List<Workshop> getLeafWorkshopsByCode(@RequestParam String workshopCode) {
		return workshopService.getLeafWorkshopsByCode(workshopCode);
	}

	/**
	 * 获取所有叶子节点
	 * @return 叶子节点列表
	 */
	@GetMapping("/leaf-nodes/all")
	public List<Workshop> getAllLeafWorkshops() {
		return workshopService.getAllLeafWorkshops();
	}

}
