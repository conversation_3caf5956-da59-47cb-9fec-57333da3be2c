package com.github.cret.web.oee.task;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.github.cret.web.oee.service.FileStorageService;
import com.github.cret.web.oee.service.TaskService;

/**
 * 导出文件清理定时任务 定期清理过期的导出文件和任务记录
 */
@Service
public class ExportFileCleanupTask {

	private static final Logger log = LoggerFactory.getLogger(ExportFileCleanupTask.class);

	private final FileStorageService fileStorageService;

	private final TaskService taskService;

	// 文件保留天数（默认7天）
	private static final int FILE_RETENTION_DAYS = 7;

	// 任务记录保留天数（默认30天）
	private static final int TASK_RETENTION_DAYS = 30;

	public ExportFileCleanupTask(FileStorageService fileStorageService, TaskService taskService) {
		this.fileStorageService = fileStorageService;
		this.taskService = taskService;
	}

	/**
	 * 每天凌晨2点执行清理任务
	 */
	@Scheduled(cron = "0 0 2 * * ?")
	public void cleanupExpiredFiles() {
		log.info("Starting export file cleanup task");

		try {
			// 清理过期文件
			int deletedFiles = fileStorageService.cleanupExpiredFiles(FILE_RETENTION_DAYS);

			// 清理过期任务记录
			int deletedTasks = taskService.cleanupExpiredTasks(TASK_RETENTION_DAYS);

			// 记录清理结果
			long directorySize = fileStorageService.getDirectorySize();
			log.info("Export file cleanup completed. Deleted {} files, {} task records. "
					+ "Current directory size: {} bytes", deletedFiles, deletedTasks, directorySize);

		}
		catch (Exception e) {
			log.error("Export file cleanup task failed", e);
		}
	}

	/**
	 * 手动触发清理任务（用于测试或紧急清理）
	 */
	public void manualCleanup() {
		log.info("Manual export file cleanup triggered");
		cleanupExpiredFiles();
	}

	/**
	 * 获取当前导出目录状态信息
	 */
	public String getDirectoryStatus() {
		try {
			int fileCount = fileStorageService.listFiles().size();
			long directorySize = fileStorageService.getDirectorySize();

			return String.format("Export directory status: %d files, %d bytes total", fileCount, directorySize);
		}
		catch (Exception e) {
			log.error("Failed to get directory status", e);
			return "Failed to get directory status: " + e.getMessage();
		}
	}

}
