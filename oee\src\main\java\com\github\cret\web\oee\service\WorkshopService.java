package com.github.cret.web.oee.service;

import java.util.List;

import com.github.cret.web.oee.document.Workshop;
import com.github.cret.web.oee.domain.workshop.WorkshopTree;

/**
 * 车间服务接口
 */
public interface WorkshopService {

	// 获取车间列表
	List<Workshop> getList();

	// 保存车间
	Workshop saveWorkshop(Workshop workshop);

	// 删除车间
	void deleteWorkshop(String id);

	// 获取车间通过ID
	Workshop getWorkshopById(String id);

	Workshop findByCode(String code);

	/**
	 * 获取车间树结构
	 * @return 车间树结构
	 */
	List<WorkshopTree> getWorkshopTree();

	/**
	 * 根据车间编码获取所有叶子节点
	 * @param workshopCode 车间编码
	 * @return 叶子节点列表
	 */
	List<Workshop> getLeafWorkshopsByCode(String workshopCode);

	/**
	 * 获取所有叶子节点
	 * @return 叶子节点列表
	 */
	List<Workshop> getAllLeafWorkshops();

}
