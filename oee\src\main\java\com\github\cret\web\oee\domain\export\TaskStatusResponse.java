package com.github.cret.web.oee.domain.export;

import com.github.cret.web.oee.document.TaskStatus;

/**
 * 任务状态响应DTO
 */
public class TaskStatusResponse {

	/**
	 * 任务ID
	 */
	private String taskId;

	/**
	 * 任务状态
	 */
	private String status;

	/**
	 * 任务进度（0-100）
	 */
	private Integer progress;

	/**
	 * 错误信息（任务失败时）
	 */
	private String errorMessage;

	/**
	 * 文件名（任务完成时）
	 */
	private String fileName;

	public TaskStatusResponse() {
	}

	public TaskStatusResponse(TaskStatus taskStatus) {
		this.taskId = taskStatus.getTaskId();
		this.status = taskStatus.getStatus().name();
		this.progress = taskStatus.getProgress();
		this.errorMessage = taskStatus.getErrorMessage();
		this.fileName = taskStatus.getFileName();
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getProgress() {
		return progress;
	}

	public void setProgress(Integer progress) {
		this.progress = progress;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

}
