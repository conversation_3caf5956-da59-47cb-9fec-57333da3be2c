package com.github.cret.web.oee.service;

import java.io.File;
import java.util.List;

/**
 * 文件存储服务接口
 */
public interface FileStorageService {

	/**
	 * 获取导出目录路径
	 * @return 导出目录路径
	 */
	String getExportDirectory();

	/**
	 * 确保导出目录存在
	 */
	void ensureExportDirectoryExists();

	/**
	 * 检查文件是否存在
	 * @param filePath 文件路径
	 * @return 文件是否存在
	 */
	boolean fileExists(String filePath);

	/**
	 * 获取文件对象
	 * @param filePath 文件路径
	 * @return 文件对象
	 */
	File getFile(String filePath);

	/**
	 * 删除文件
	 * @param filePath 文件路径
	 * @return 删除是否成功
	 */
	boolean deleteFile(String filePath);

	/**
	 * 清理过期文件
	 * @param daysToKeep 保留天数
	 * @return 清理的文件数量
	 */
	int cleanupExpiredFiles(int daysToKeep);

	/**
	 * 获取目录下的所有文件
	 * @return 文件列表
	 */
	List<File> listFiles();

	/**
	 * 获取文件大小（字节）
	 * @param filePath 文件路径
	 * @return 文件大小
	 */
	long getFileSize(String filePath);

	/**
	 * 获取目录总大小（字节）
	 * @return 目录总大小
	 */
	long getDirectorySize();

}
